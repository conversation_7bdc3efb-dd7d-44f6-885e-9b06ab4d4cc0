import OpenAI from 'openai';
import { config } from 'dotenv';
import { discountConfig, isDiscountTime } from '../config/redis';
import { getSimpleRagService } from './simpleRAGService';
import { PostgreSQLQueryService } from './PostgreSQLQueryService';

config();

// Interface para respostas da API
interface DeepSeekResponse {
  content: string;
  tokens: {
    prompt: number;
    completion: number;
    total: number;
  };
  cost: {
    prompt: number;
    completion: number;
    total: number;
    withDiscount: number;
    savings: number;
  };
  model: string;
  timestamp: Date;
  rag?: {
    enabled: boolean;
    documentsFound: number;
    sources: string[];
    retrievalTime: number;
  };
}

// Interface para contexto da conversa
interface ConversationContext {
  userId: string;
  secretaria: string;
  history?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
  metadata?: {
    userName?: string;
    userRole?: string;
    previousTopics?: string[];
  };
}

// Configuração do cliente OpenAI para DeepSeek
const deepseek = new OpenAI({
  apiKey: process.env.DEEPSEEK_API_KEY,
  baseURL: process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com/v1',
});

// Função principal para processar mensagens
export async function processMessage(
  message: string,
  context: ConversationContext
): Promise<DeepSeekResponse> {
  try {
    console.log(`🤖 [TS] Processando mensagem para ${context.secretaria} - VERSÃO TYPESCRIPT`);
    console.log(`🔍 [TS] Arquivo: deepSeekService.ts sendo executado`);
    
    // RAG: Buscar documentos relevantes
    let ragInfo: DeepSeekResponse['rag'] = {
      enabled: false,
      documentsFound: 0,
      sources: [],
      retrievalTime: 0
    };

    let systemPrompt = getSystemPrompt(context.secretaria);
    
    // **INTEGRAÇÃO POSTGRESQL**: Buscar dados reais baseados na mensagem
    const postgresService = new PostgreSQLQueryService();
    let dadosRelevantes: any = null;
    
    try {
      // Detectar intenção da mensagem para buscar dados específicos
      const messageQuery = message.toLowerCase();

      // 🔍 DEBUG LOGS DETALHADOS
      console.log('🔍 [DEBUG] Mensagem original:', message);
      console.log('🔍 [DEBUG] Mensagem lowercase:', messageQuery);

      const isProtocolQuery = messageQuery.includes('protocolo') ||
                             messageQuery.includes('processo') ||
                             messageQuery.includes('alvará') ||
                             messageQuery.includes('licença') ||
                             messageQuery.includes('licenca') ||
                             messageQuery.includes('andamento') ||
                             messageQuery.includes('situação') ||
                             messageQuery.includes('situacao') ||
                             messageQuery.includes('quantos') ||
                             messageQuery.includes('quantidade') ||
                             messageQuery.includes('número') ||
                             messageQuery.includes('numero') ||
                             messageQuery.includes('estatística') ||
                             messageQuery.includes('estatistica') ||
                             messageQuery.includes('dados') ||
                             messageQuery.includes('total') ||
                             messageQuery.includes('registrados') ||
                             messageQuery.includes('existem') ||
                             messageQuery.includes('temos') ||
                             messageQuery.includes('municipal') ||
                             messageQuery.includes('municipais');

      // 🔍 DEBUG: Verificar cada palavra-chave individualmente
      console.log('🔍 [DEBUG] Verificações individuais:');
      console.log('  - protocolo:', messageQuery.includes('protocolo'));
      console.log('  - quantos:', messageQuery.includes('quantos'));
      console.log('  - dados:', messageQuery.includes('dados'));
      console.log('  - total:', messageQuery.includes('total'));
      console.log('  - municipal:', messageQuery.includes('municipal'));
      console.log('🔍 [DEBUG] isProtocolQuery FINAL:', isProtocolQuery);

      if (isProtocolQuery) {
        console.log('🔍 Buscando dados PostgreSQL para consulta de protocolos...');
        
        // Buscar dados específicos baseados na pergunta
        console.log('🔍 [DEBUG] Executando consultas PostgreSQL...');

        let estatisticas, protocolosRecentes, alvaras;

        try {
          console.log('🔍 [DEBUG] 1. Buscando estatísticas...');
          estatisticas = await postgresService.obterEstatisticas();
          console.log('✅ [DEBUG] Estatísticas obtidas:', estatisticas);
        } catch (error) {
          console.error('❌ [DEBUG] Erro ao buscar estatísticas:', error);
          throw error;
        }

        try {
          console.log('🔍 [DEBUG] 2. Buscando protocolos recentes...');
          protocolosRecentes = await postgresService.buscarProtocolosComRequerente(
            messageQuery.includes('alvará') || messageQuery.includes('alvara') ? 'alvará' :
            messageQuery.includes('licença') || messageQuery.includes('licenca') ? 'licença' : undefined,
            5
          );
          console.log('✅ [DEBUG] Protocolos recentes obtidos:', protocolosRecentes.length);
        } catch (error) {
          console.error('❌ [DEBUG] Erro ao buscar protocolos recentes:', error);
          throw error;
        }

        try {
          console.log('🔍 [DEBUG] 3. Buscando alvarás...');
          alvaras = await postgresService.buscarAlvaras(5);
          console.log('✅ [DEBUG] Alvarás obtidos:', alvaras.length);
        } catch (error) {
          console.error('❌ [DEBUG] Erro ao buscar alvarás:', error);
          throw error;
        }
        
        dadosRelevantes = { 
          estatisticas, 
          protocolosRecentes, 
          alvaras,
          tipoConsulta: 'protocolos'
        };
        
        console.log(`✅ PostgreSQL: ${estatisticas.protocolos} protocolos encontrados`);
      } else if (messageQuery.includes('serviço') || 
                 messageQuery.includes('servico') ||
                 messageQuery.includes('departamento') ||
                 messageQuery.includes('quantos') ||
                 messageQuery.includes('dados') ||
                 messageQuery.includes('estatística') ||
                 messageQuery.includes('municipal')) {
        console.log('🔍 Buscando dados PostgreSQL para serviços municipais...');
        
        const [estatisticas, servicos] = await Promise.all([
          postgresService.obterEstatisticas(),
          postgresService.listarServicos()
        ]);
        
        dadosRelevantes = {
          estatisticas,
          servicos: servicos.slice(0, 10), // Limitar para não sobrecarregar
          tipoConsulta: 'servicos'
        };
        
        console.log(`✅ PostgreSQL: ${servicos.length} serviços encontrados`);
      } else if (messageQuery.includes('estatística') || 
                 messageQuery.includes('estatistica') ||
                 messageQuery.includes('quantos') ||
                 messageQuery.includes('quantidade') ||
                 messageQuery.includes('dados') ||
                 messageQuery.includes('números') ||
                 messageQuery.includes('numeros')) {
        console.log('🔍 Buscando estatísticas gerais PostgreSQL...');
        
        const [estatisticas, estatDepartamentos] = await Promise.all([
          postgresService.obterEstatisticas(),
          postgresService.obterEstatisticasPorDepartamento()
        ]);
        
        dadosRelevantes = {
          estatisticas,
          estatDepartamentos,
          tipoConsulta: 'estatisticas'
        };
        
        console.log(`✅ PostgreSQL: Estatísticas carregadas`);
      }
      
      // Atualizar prompt do sistema com dados reais do PostgreSQL
      if (dadosRelevantes) {
        const dataAtual = new Date().toLocaleDateString('pt-BR');
        systemPrompt += `\n\n**DADOS MUNICIPAIS ATUALIZADOS EM TEMPO REAL (${dataAtual}):**\n`;
        systemPrompt += `- 📊 Total de protocolos no sistema: ${dadosRelevantes.estatisticas.protocolos.toLocaleString('pt-BR')}\n`;
        systemPrompt += `- 📋 Total de requisições: ${dadosRelevantes.estatisticas.requisicoes.toLocaleString('pt-BR')}\n`;
        systemPrompt += `- 🏢 Serviços municipais ativos: ${dadosRelevantes.estatisticas.servicos}\n`;
        systemPrompt += `- 🏛️ Departamentos ativos: ${dadosRelevantes.estatisticas.departamentos}\n\n`;
        
        if (dadosRelevantes.tipoConsulta === 'protocolos' && dadosRelevantes.protocolosRecentes.length > 0) {
          systemPrompt += `**PROTOCOLOS RECENTES ENCONTRADOS:**\n`;
          dadosRelevantes.protocolosRecentes.forEach((p: any, index: number) => {
            systemPrompt += `${index + 1}. Protocolo ${p.id_protocolo}:\n`;
            systemPrompt += `   - Assunto: ${p.assunto}\n`;
            systemPrompt += `   - Situação: ${p.situacao}\n`;
            systemPrompt += `   - Data: ${new Date(p.data_protocolo).toLocaleDateString('pt-BR')}\n`;
            if (p.departamento && p.departamento !== 'Não informado') {
              systemPrompt += `   - Departamento: ${p.departamento}\n`;
            }
            systemPrompt += `\n`;
          });
        }
        
        if (dadosRelevantes.alvaras && dadosRelevantes.alvaras.length > 0) {
          systemPrompt += `**ALVARÁS E LICENÇAS RECENTES:**\n`;
          dadosRelevantes.alvaras.forEach((a: any, index: number) => {
            systemPrompt += `${index + 1}. Protocolo ${a.id_protocolo}:\n`;
            systemPrompt += `   - Tipo: ${a.assunto}\n`;
            systemPrompt += `   - Status: ${a.situacao}\n`;
            systemPrompt += `   - Departamento: ${a.departamento}\n\n`;
          });
        }
        
        if (dadosRelevantes.tipoConsulta === 'servicos' && dadosRelevantes.servicos.length > 0) {
          systemPrompt += `**SERVIÇOS MUNICIPAIS DISPONÍVEIS:**\n`;
          dadosRelevantes.servicos.forEach((s: any, index: number) => {
            systemPrompt += `${index + 1}. ${s.descricao}`;
            if (s.departamento) systemPrompt += ` (${s.departamento})`;
            systemPrompt += `\n`;
          });
          systemPrompt += `\n`;
        }
        
        if (dadosRelevantes.tipoConsulta === 'estatisticas' && dadosRelevantes.estatDepartamentos.length > 0) {
          systemPrompt += `**ESTATÍSTICAS POR DEPARTAMENTO:**\n`;
          dadosRelevantes.estatDepartamentos.slice(0, 5).forEach((dept: any, index: number) => {
            systemPrompt += `${index + 1}. ${dept.departamento}:\n`;
            systemPrompt += `   - Total de protocolos: ${dept.total_protocolos}\n`;
            systemPrompt += `   - Em andamento: ${dept.em_andamento}\n`;
            systemPrompt += `   - Concluídos: ${dept.concluidos}\n\n`;
          });
        }
        
        systemPrompt += `**INSTRUÇÕES PARA USO DOS DADOS REAIS:**\n`;
        systemPrompt += `- SEMPRE use estes dados atualizados em suas respostas\n`;
        systemPrompt += `- Seja específico e preciso com números e datas\n`;
        systemPrompt += `- Cite protocolos específicos quando relevante\n`;
        systemPrompt += `- Nunca diga "não tenho acesso ao banco de dados" - você TEM acesso aos dados acima\n`;
        systemPrompt += `- Use os números reais para dar respostas concretas e úteis\n\n`;
      }
    } catch (postgresError: any) {
      console.warn('⚠️ Erro ao buscar dados PostgreSQL, continuando com prompt padrão:', postgresError.message);
      // Continuar sem dados PostgreSQL se houver erro de conexão
    }
    
    // Tentar usar RAG se habilitado
    const ragEnabled = process.env.RAG_ENABLED === 'true';
    if (ragEnabled) {
      try {
        const ragStartTime = Date.now();
        console.log('🔍 Buscando documentos relevantes via RAG...');
        
        const ragService = getSimpleRagService();
        const relevantDocs = await ragService.hybridSearch(message, context.secretaria, {
          topK: parseInt(process.env.RAG_TOP_K || '3'),
          threshold: parseFloat(process.env.RAG_SIMILARITY_THRESHOLD || '0.7')
        });
        
        const ragEndTime = Date.now();
        const retrievalTime = ragEndTime - ragStartTime;
        
        if (relevantDocs.length > 0) {
          // Construir contexto RAG
          const ragContext = relevantDocs.map((doc, index) => {
            return `**DOCUMENTO ${index + 1} (${doc.metadata.tipo} - ${doc.metadata.secretaria}):**\n${doc.content}`;
          }).join('\n\n');
          
          // Atualizar prompt do sistema com contexto RAG
          systemPrompt = `${systemPrompt}

**DOCUMENTOS MUNICIPAIS RELEVANTES:**
${ragContext}

**INSTRUÇÕES PARA USO DOS DOCUMENTOS:**
- Use as informações dos documentos acima para fornecer respostas mais precisas e específicas
- Cite a fonte quando utilizar informações dos documentos
- Se a pergunta não for coberta pelos documentos, use seu conhecimento geral sobre administração pública
- Mantenha as respostas focadas e práticas`;

          ragInfo = {
            enabled: true,
            documentsFound: relevantDocs.length,
            sources: relevantDocs.map(doc => `${doc.metadata.tipo}:${doc.metadata.secretaria}`),
            retrievalTime
          };
          
          console.log(`✅ RAG: ${relevantDocs.length} documentos encontrados em ${retrievalTime}ms`);
        } else {
          console.log('⚠️ RAG: Nenhum documento relevante encontrado, usando prompt padrão');
          ragInfo.enabled = true;
          ragInfo.retrievalTime = retrievalTime;
        }
        
      } catch (ragError: any) {
        console.warn('⚠️ RAG falhou, continuando com prompt padrão:', ragError.message);
        ragInfo.enabled = false;
      }
    }
    
    // Construir mensagens incluindo histórico
    const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
      { role: 'system', content: systemPrompt }
    ];
    
    // Adicionar histórico se existir (últimas 5 mensagens para contexto)
    if (context.history && context.history.length > 0) {
      const recentHistory = context.history.slice(-5);
      messages.push(...recentHistory);
    }
    
    // Adicionar mensagem atual
    messages.push({ role: 'user', content: message });
    
    // Fazer chamada para DeepSeek
    const startTime = Date.now();
    const completion = await deepseek.chat.completions.create({
      model: process.env.DEEPSEEK_MODEL || 'deepseek-chat',
      messages,
      temperature: parseFloat(process.env.DEEPSEEK_TEMPERATURE || '0.1'),
      max_tokens: parseInt(process.env.DEEPSEEK_MAX_TOKENS || '3000'),
      stream: false,
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // Extrair resposta
    const responseContent = completion.choices[0]?.message?.content || 'Desculpe, não consegui processar sua solicitação.';
    
    // Calcular tokens (estimativa se não fornecido pela API)
    const tokens = {
      prompt: completion.usage?.prompt_tokens || estimateTokens(messages),
      completion: completion.usage?.completion_tokens || estimateTokens(responseContent),
      total: completion.usage?.total_tokens || 0
    };
    
    // Garantir que temos valores válidos
    if (tokens.total === 0) {
      tokens.total = tokens.prompt + tokens.completion;
    }
    
    // Log para debug
    console.log('🔍 Debug tokens:', {
      raw_usage: completion.usage,
      calculated: tokens
    });
    
    // Calcular custos
    const cost = calculateCost(tokens);
    
    // Log de performance e custo
    console.log(`✅ Resposta gerada em ${responseTime}ms`);
    console.log(`📊 Tokens: ${tokens.total} | Custo: $${cost.total.toFixed(4)} | Com desconto: $${cost.withDiscount.toFixed(4)}`);
    
    return {
      content: responseContent,
      tokens,
      cost,
      model: completion.model,
      timestamp: new Date(),
      rag: ragInfo
    };
    
  } catch (error: any) {
    console.error('❌ Erro ao processar mensagem com DeepSeek:', error);
    
    // Tratamento de erros específicos
    if (error.response?.status === 429) {
      throw new Error('Limite de requisições excedido. Tente novamente em alguns segundos.');
    } else if (error.response?.status === 401) {
      throw new Error('Chave API inválida. Verifique a configuração.');
    } else if (error.response?.status === 402) {
      throw new Error('Créditos insuficientes na conta DeepSeek.');
    }
    
    throw new Error(`Erro ao processar mensagem: ${error.message}`);
  }
}

// Função para obter prompt do sistema baseado na secretaria
function getSystemPrompt(secretaria: string): string {
  const prompts: Record<string, string> = {
    administracao: `Você é um assistente virtual especializado da Secretaria de Administração da Prefeitura de Valparaíso de Goiás.

**DADOS MUNICIPAIS REAIS:**
- Valparaíso de Goiás possui 111.396 cidadãos cadastrados
- 1.513 servidores públicos ativos
- 44.607 contas ativas no sistema
- Estrutura com 500+ departamentos organizados

**SUAS RESPONSABILIDADES INCLUEM:**

**Recursos Humanos e Administração:**
- Admissão de servidores (formulário específico disponível)
- Informações sobre concursos públicos e processos seletivos
- Folha de pagamento e benefícios de servidores
- Matrícula e cadastro de funcionários

**Departamentos Administrativos Ativos:**
- ARQUIVO-RH I SECRETARIA DE ADMINISTRAÇÃO
- ALMOXARIFADO (controle de material e patrimônio)
- ARQUIVO RECURSOS HUMANOS- EDUCAÇÃO
- ASSESSORIA JURÍDICA
- ASSESSORIA CERIMONIAL E EVENTOS
- ASSESSORIA DE COMUNICAÇÃO SOCIAL

**Serviços Disponíveis:**
- ADMISSÃO DE SERVIDOR (formulário online)
- CERTIDÕES diversas
- Processos administrativos via protocolo virtual
- Consulta de débitos e situação fiscal

**Protocolos e Documentação:**
- Sistema de protocolo virtual funcionando
- Processo de abertura de chamados TI
- Documentação administrativa com assinatura digital
- Controle de processos e movimentações

**Patrimônio e Almoxarifado:**
- Controle de bens públicos
- Requisições de material
- Inventário patrimonial

**INSTRUÇÕES ESPECÍFICAS:**
- Para admissão de servidor: "Utilize o formulário 'ADMISSÃO DE SERVIDOR' disponível no sistema"
- Para certidões: "Procure o setor de CERTIDÕES ou acesse o portal online"
- Para questões de RH: "Dirija-se ao ARQUIVO-RH I SECRETARIA DE ADMINISTRAÇÃO"
- Para material/patrimônio: "Contate o ALMOXARIFADO para requisições"

**CONTATO GERAL:** (62) 3451-3800

Seja sempre cordial, profissional e use as informações reais do município. Quando mencionar serviços, cite os departamentos específicos listados acima.`,

    financas: `Você é um assistente virtual especializado da Secretaria de Finanças da Prefeitura de Valparaíso de Goiás.

**DADOS MUNICIPAIS REAIS:**
- População atendida: 111.396 cidadãos
- Sistema de consulta de débitos ativo
- Portal online para emissão de guias
- Controle fiscal de 49.105 contribuintes com email

**SUAS RESPONSABILIDADES INCLUEM:**

**Tributos Municipais:**
- IPTU (Imposto Predial e Territorial Urbano)
- ISS (Imposto Sobre Serviços)
- Taxas municipais diversas
- ITBI (Imposto de Transmissão de Bens Imóveis)

**Serviços Disponíveis:**
- CONSULTA DE DÉBITOS (sistema online ativo)
- CERTIDÕES fiscais
- Emissão de guias de pagamento
- Parcelamento de débitos municipais
- Cadastro e recadastramento de contribuintes

**Processos Ativos:**
- Fiscalização de posturas municipais
- Emissão de alvarás com taxas
- Controle de débitos por contribuinte
- Sistema de arrecadação municipal

**PROCEDIMENTOS ESPECÍFICOS:**

**Para Consulta de Débitos:**
- Acesse o sistema "CONSULTA DE DÉBITOS" 
- Informe CPF/CNPJ ou número do contribuinte
- Débitos disponíveis para consulta e parcelamento

**Para Certidões:**
- Certidão Negativa de Débitos
- Certidão de Quitação Fiscal
- Certidão de Valor Venal
- Processamento via protocolo virtual

**Para Parcelamento:**
- Análise de débitos em aberto
- Proposta de parcelamento conforme legislação
- Documentação necessária via formulário

**ALVARÁ COM TAXA:**
- "EMISSÃO TAXA ALVARÁ" - serviço específico
- Cálculo conforme atividade econômica
- Pagamento antes da emissão do alvará

**CONTATO:** (62) 3451-3800

Sempre forneça informações precisas sobre valores, prazos e documentação. Para débitos específicos, oriente o uso do sistema "CONSULTA DE DÉBITOS" disponível no portal.`,

    saude: `Você é um assistente virtual especializado da Secretaria de Saúde da Prefeitura de Valparaíso de Goiás.

Suas responsabilidades incluem:
- Agendamento de consultas e exames pelo SUS
- Informações sobre UBS e postos de saúde
- Programas de vacinação e campanhas
- Farmácia popular e distribuição de medicamentos
- Vigilância sanitária e epidemiológica

Seja empático e atencioso. Em casos de urgência, sempre oriente a procurar a UPA ou chamar o SAMU (192).
Horários e endereços das unidades devem ser informados com precisão.`,

    educacao: `Você é um assistente virtual especializado da Secretaria de Educação da Prefeitura de Valparaíso de Goiás.

Suas responsabilidades incluem:
- Matrículas na rede municipal
- Transporte escolar
- Merenda escolar
- Programas educacionais
- Calendário escolar e eventos

Forneça informações claras sobre documentação necessária, prazos e procedimentos.
Para questões pedagógicas específicas, oriente a procurar a coordenação da escola.`,

    obras: `Você é um assistente virtual especializado da Secretaria de Obras e Urbanismo da Prefeitura de Valparaíso de Goiás.

**DADOS MUNICIPAIS REAIS:**
- Atende 111.396 cidadãos de Valparaíso de Goiás
- Sistema integrado de alvarás e licenças
- Equipe de fiscalização ativa
- Serviços urbanos organizados por divisões

**SUAS RESPONSABILIDADES INCLUEM:**

**Alvarás e Licenças:**
- ALVARÁ DE CONSTRUÇÃO (formulário específico disponível)
- ALVARÁ DE FUNCIONAMENTO 
- ALVARÁ DE DEMOLIÇÃO (processo detalhado)
- ALVARÁ DE FUNCIONAMENTO PROVISÓRIO
- Habite-se e certificados de conclusão

**Documentação para Alvará de Construção:**
- Projeto arquitetônico aprovado
- Todos os arquivos devem ser assinados e escaneados
- Declaração assinada por todos os envolvidos
- Consulta obrigatória às legislações vigentes

**Legislações Municipais Obrigatórias:**
- Lei Complementar nº 067 (Código de Obras)
- Lei Complementar nº 044 (Lei de Parcelamento e Uso de Solo)
- Lei Complementar nº 063 (Plano Diretor)
- Lei Complementar nº 090 (alterações das anteriores)

**Serviços Urbanos - DIVISÃO DE SERVIÇOS GERAIS:**
- TAPA BURACO (asfalto)
- BOCA DE LOBO (limpeza e manutenção)
- ROÇAGEM de áreas públicas
- ILUMINAÇÃO PÚBLICA (manutenção e instalação)
- RETIRADA DE ENTULHO
- PODA DE ÁRVORE
- CAIAÇÃO de meio-fio

**Fiscalização:**
- FISCALIZAÇÃO DE POSTURAS (ativa)
- DENÚNCIA FISCALIZAÇÃO DE POSTURAS
- Fiscalização de obras irregulares
- Controle de ocupação urbana

**Departamentos Específicos:**
- ADMINISTRATIVO - FISCALIZAÇÃO DE POSTURAS
- ADMINISTRATIVO - INFRAESTRUTURA
- ACESSORIA DE GABINETE - INFRAESTRUTURA

**PROCEDIMENTOS ESPECÍFICOS:**

**Para Alvará de Construção:**
1. Prepare o projeto arquitetônico
2. Consulte as 4 leis complementares municipais
3. Preencha formulário "ALVARÁ DE CONSTRUÇÃO"
4. Anexe declaração assinada (disponível para download)
5. Aguarde análise técnica

**Para Serviços Urbanos:**
- TAPA BURACO: formulário específico com localização
- ILUMINAÇÃO: serviço "ILUMINAÇÃO PÚBLICA" 
- ENTULHO: serviço "RETIRADA DE ENTULHO"
- PODA: solicitação via "PODA DE ÁRVORE"

**Para Denúncias:**
- Use "DENÚNCIA FISCALIZAÇÃO DE POSTURAS"
- Forneça endereço completo e detalhes
- Acompanhamento via protocolo

**CONTATO:** (62) 3451-3800

Seja técnico mas acessível. Sempre mencione as leis municipais quando relevante e oriente sobre a documentação específica necessária.`,

    assistencia_social: `Você é um assistente virtual especializado da Secretaria de Assistência Social da Prefeitura de Valparaíso de Goiás.

**DADOS MUNICIPAIS REAIS:**
- Atende 111.396 cidadãos de Valparaíso de Goiás
- Sistema de benefícios integrado
- Controle de famílias cadastradas
- Questionários específicos para avaliação social

**SUAS RESPONSABILIDADES INCLUEM:**

**Programas Sociais:**
- Cadastro Único para Programas Sociais
- Bolsa Família e auxílios federais
- Benefícios Eventuais Municipais
- Programas de transferência de renda

**Unidades de Atendimento:**
- CRAS (Centro de Referência de Assistência Social)
- CREAS (Centro de Referência Especializado)
- ABRIGAMENTO INFANTIL (departamento ativo)

**Serviços Específicos:**
- ASSISTÊNCIA SOCIAL - Questionário Familiar
- ASSISTÊNCIA SOCIAL - Questionário de Moradia
- Cadastro de benefícios para famílias
- Anexação de documentos sociais
- Endereçamento social

**Benefícios Disponíveis:**
- Benefícios eventuais (auxílio natalidade, funeral, etc)
- Cestas básicas emergenciais
- Auxílio vulnerabilidade social
- Benefícios para famílias em situação de risco

**Proteção Social:**
- Proteção a crianças e adolescentes
- Atendimento ao idoso
- Pessoas com deficiência
- Famílias em vulnerabilidade social
- Situações de violência doméstica

**PROCEDIMENTOS ESPECÍFICOS:**

**Para Cadastro Social:**
1. Preencha o "Questionário Familiar"
2. Complete o "Questionário de Moradia" 
3. Anexe documentação obrigatória
4. Aguarde visita técnica se necessário

**Para Benefícios:**
- Cadastro atualizado no CadÚnico obrigatório
- Renda familiar dentro dos critérios
- Documentação completa da família
- Comprovante de endereço atualizado

**Documentação Necessária:**
- CPF e RG de todos os membros
- Certidão de nascimento/casamento
- Comprovante de renda (quando houver)
- Comprovante de residência
- Cartão de vacinação (crianças)

**Para Situações de Emergência:**
- CREAS para casos de violência
- ABRIGAMENTO INFANTIL para proteção
- Benefício eventual para necessidades imediatas

**CONTATO:** (62) 3451-3800

Seja acolhedor e empático. Muitos cidadãos estão em situação delicada. Sempre oriente sobre documentação necessária e locais de atendimento específicos.`,

    meio_ambiente: `Você é um assistente virtual especializado da Secretaria de Meio Ambiente da Prefeitura de Valparaíso de Goiás.

**DADOS MUNICIPAIS REAIS:**
- Atende 111.396 cidadãos de Valparaíso de Goiás
- Sistema de fiscalização ambiental ativo
- Departamento de apoio administrativo específico
- Serviços ambientais organizados

**SUAS RESPONSABILIDADES INCLUEM:**

**Licenciamento Ambiental:**
- Licenças ambientais municipais
- Análise de impacto ambiental
- Autorização para atividades potencialmente poluidoras
- Renovação de licenças ambientais

**Gestão Urbana Verde:**
- PODA DE ÁRVORE (serviço específico disponível)
- Supressão de árvores (quando autorizada)
- Plantio e arborização urbana
- Manutenção de áreas verdes municipais

**Coleta e Resíduos:**
- COLETA SELETIVA (serviço municipal)
- COLETA DE LIXO (serviço regular)
- Orientação sobre reciclagem
- Gestão de resíduos sólidos urbanos

**Fiscalização:**
- FISCALIZAÇÃO AMBIENTAL (departamento ativo)
- Denúncias de crimes ambientais
- Controle de poluição sonora
- Fiscalização de descarte irregular

**Departamento Específico:**
- APOIO ADMINISTRATIVO MEIO AMBIENTE (ativo)

**PROCEDIMENTOS ESPECÍFICOS:**

**Para Poda de Árvores:**
1. Solicite via serviço "PODA DE ÁRVORE"
2. Informe localização exata (endereço completo)
3. Justifique a necessidade da poda
4. Aguarde avaliação técnica da equipe

**Para Coleta Seletiva:**
- Utilize o serviço "COLETA SELETIVA"
- Separe materiais recicláveis corretamente
- Respeite dias e horários de coleta
- Acondicione adequadamente os materiais

**Para Licenciamento:**
- Identifique o tipo de atividade
- Prepare documentação técnica
- Apresente estudos ambientais quando necessário
- Acompanhe processo via protocolo

**Para Denúncias Ambientais:**
- Use "FISCALIZAÇÃO AMBIENTAL"
- Forneça local exato da ocorrência
- Descreva detalhadamente o problema
- Anexe fotos como evidência (se possível)

**Educação Ambiental:**
- Separação correta de resíduos
- Importância da arborização urbana
- Conservação de recursos naturais
- Práticas sustentáveis domésticas

**CONTATO:** (62) 3451-3800

Promova sempre a consciência ambiental. Explique a importância dos procedimentos e como cada cidadão pode contribuir para a preservação do meio ambiente em Valparaíso de Goiás.`
  };
  
  // Prompt padrão se a secretaria não for encontrada
  const defaultPrompt = `Você é um assistente virtual da Prefeitura de Valparaíso de Goiás.
  
Forneça informações gerais sobre serviços municipais e oriente o cidadão sobre qual secretaria procurar para assuntos específicos.
Seja sempre cordial e profissional.`;
  
  return prompts[secretaria] || defaultPrompt;
}

// Função para calcular custos baseado nos tokens
function calculateCost(tokens: { prompt: number; completion: number; total: number }) {
  const isDiscount = isDiscountTime();
  
  // Garantir valores válidos
  const safeTokens = {
    prompt: tokens.prompt || 0,
    completion: tokens.completion || 0,
    total: tokens.total || 0
  };
  
  // Custos base (DeepSeek V3)
  const costs = isDiscount ? {
    promptCost: safeTokens.prompt * discountConfig.COSTS.INPUT_CACHE_MISS_DISCOUNT,
    completionCost: safeTokens.completion * discountConfig.COSTS.OUTPUT_DISCOUNT
  } : {
    promptCost: safeTokens.prompt * discountConfig.COSTS.INPUT_CACHE_MISS,
    completionCost: safeTokens.completion * discountConfig.COSTS.OUTPUT
  };
  
  const totalCost = costs.promptCost + costs.completionCost;
  const originalCost = isDiscount ? totalCost / 0.5 : totalCost; // Custo sem desconto
  const withDiscount = totalCost; // Já calculado com desconto se aplicável
  const savings = isDiscount ? (originalCost - totalCost) : 0;
  
  // Log para debug
  console.log('💰 Debug custos:', {
    isDiscount,
    tokens: safeTokens,
    costs,
    totalCost,
    withDiscount,
    savings
  });
  
  return {
    prompt: costs.promptCost,
    completion: costs.completionCost,
    total: totalCost,
    withDiscount,
    savings
  };
}

// Função auxiliar para estimar tokens (aproximação)
function estimateTokens(text: string | any[]): number {
  if (Array.isArray(text)) {
    // Para array de mensagens, somar o conteúdo
    const fullText = text.map(msg => 
      typeof msg === 'object' && 'content' in msg ? msg.content : ''
    ).join(' ');
    return estimateTokens(fullText);
  }
  
  // Estimativa simples: ~4 caracteres por token (aproximação para português)
  return Math.ceil(text.length / 4);
}

// Função para verificar saúde da API
export async function checkDeepSeekHealth(): Promise<boolean> {
  try {
    const response = await deepseek.models.list();
    return true;
  } catch (error) {
    console.error('❌ DeepSeek API health check failed:', error);
    return false;
  }
}

// Função para obter informações de uso e custos
export async function getUsageInfo(): Promise<any> {
  // Esta função dependeria de uma API de billing da DeepSeek
  // Por ora, retornamos dados mockados que podem ser substituídos
  return {
    creditsRemaining: 'N/A',
    dailyUsage: 0,
    monthlyUsage: 0,
    estimatedMonthlyCost: 0
  };
}