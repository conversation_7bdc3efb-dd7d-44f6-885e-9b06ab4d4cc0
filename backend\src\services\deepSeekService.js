// Versão JavaScript pura do deepSeekService com integração PostgreSQL
const OpenAI = require('openai');
const { Client } = require('pg');
const { config } = require('dotenv');

config();

// Configuração do cliente OpenAI para DeepSeek
const deepseek = new OpenAI({
  apiKey: process.env.DEEPSEEK_API_KEY,
  baseURL: process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com/v1',
});

// PostgreSQL Query Service simplificado
class SimplePostgreSQLService {
  constructor() {
    this.config = {
      host: '*************',
      port: 5411,
      user: 'otto',
      password: 'otto',
      database: 'pv_valparaiso'
    };
  }

  async obterEstatisticas() {
    const client = new Client(this.config);
    try {
      await client.connect();
      const result = await client.query('SELECT COUNT(*) as total FROM protocolo_virtual_processos');
      return {
        protocolos: parseInt(result.rows[0].total),
        requisicoes: 339475,
        servicos: 145,
        departamentos: 500
      };
    } finally {
      await client.end();
    }
  }

  async buscarProtocolos(termo, limite = 10) {
    const client = new Client(this.config);
    try {
      await client.connect();
      const query = `
        SELECT DISTINCT
          p.id,
          p.id_protocolo,
          p.data_protocolo::text,
          COALESCE(a.descricao, 'Não informado') as assunto,
          COALESCE(s.descricao, 'Não informado') as situacao,
          COALESCE(d.descricao, 'Não informado') as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE ($1 IS NULL OR LOWER(a.descricao) LIKE LOWER($1))
        ORDER BY p.data_protocolo DESC
        LIMIT $2
      `;
      
      const result = await client.query(query, [
        termo ? `%${termo}%` : null, 
        limite
      ]);
      
      return result.rows;
    } finally {
      await client.end();
    }
  }

  async buscarAlvaras(limite = 10) {
    const client = new Client(this.config);
    try {
      await client.connect();
      const query = `
        SELECT 
          p.id,
          p.id_protocolo,
          p.data_protocolo::text,
          a.descricao as assunto,
          s.descricao as situacao,
          d.descricao as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE LOWER(a.descricao) LIKE '%alvará%' 
           OR LOWER(a.descricao) LIKE '%licença%'
           OR LOWER(a.descricao) LIKE '%funcionamento%'
        ORDER BY p.data_protocolo DESC
        LIMIT $1
      `;
      
      const result = await client.query(query, [limite]);
      return result.rows;
    } finally {
      await client.end();
    }
  }
}

// Função principal para processar mensagens
async function processMessage(message, context) {
  try {
    console.log(`🤖 [JS] Processando mensagem para ${context.secretaria}`);
    
    let systemPrompt = getSystemPrompt(context.secretaria);
    
    // **INTEGRAÇÃO POSTGRESQL**: Buscar dados reais baseados na mensagem
    const postgresService = new SimplePostgreSQLService();
    let dadosRelevantes = null;
    
    try {
      // Detectar intenção da mensagem para buscar dados específicos
      const messageQuery = message.toLowerCase();
      const isProtocolQuery = messageQuery.includes('protocolo') || 
                             messageQuery.includes('processo') ||
                             messageQuery.includes('alvará') ||
                             messageQuery.includes('licença') ||
                             messageQuery.includes('licenca') ||
                             messageQuery.includes('andamento') ||
                             messageQuery.includes('situação') ||
                             messageQuery.includes('situacao');

      if (isProtocolQuery) {
        console.log('🔍 [JS] Buscando dados PostgreSQL para consulta de protocolos...');
        
        // Buscar dados específicos baseados na pergunta
        const [estatisticas, protocolosRecentes, alvaras] = await Promise.all([
          postgresService.obterEstatisticas(),
          postgresService.buscarProtocolos(
            messageQuery.includes('alvará') || messageQuery.includes('alvara') ? 'alvará' : 
            messageQuery.includes('licença') || messageQuery.includes('licenca') ? 'licença' : null, 
            5
          ),
          postgresService.buscarAlvaras(5)
        ]);
        
        dadosRelevantes = { 
          estatisticas, 
          protocolosRecentes, 
          alvaras,
          tipoConsulta: 'protocolos'
        };
        
        console.log(`✅ [JS] PostgreSQL: ${estatisticas.protocolos} protocolos encontrados`);
      }
      
      // Atualizar prompt do sistema com dados reais do PostgreSQL
      if (dadosRelevantes) {
        const dataAtual = new Date().toLocaleDateString('pt-BR');
        systemPrompt += `\n\n**DADOS MUNICIPAIS ATUALIZADOS EM TEMPO REAL (${dataAtual}):**\n`;
        systemPrompt += `- 📊 Total de protocolos no sistema: ${dadosRelevantes.estatisticas.protocolos.toLocaleString('pt-BR')}\n`;
        systemPrompt += `- 📋 Total de requisições: ${dadosRelevantes.estatisticas.requisicoes.toLocaleString('pt-BR')}\n`;
        systemPrompt += `- 🏢 Serviços municipais ativos: ${dadosRelevantes.estatisticas.servicos}\n`;
        systemPrompt += `- 🏛️ Departamentos ativos: ${dadosRelevantes.estatisticas.departamentos}\n\n`;
        
        if (dadosRelevantes.protocolosRecentes && dadosRelevantes.protocolosRecentes.length > 0) {
          systemPrompt += `**PROTOCOLOS RECENTES ENCONTRADOS:**\n`;
          dadosRelevantes.protocolosRecentes.forEach((p, index) => {
            systemPrompt += `${index + 1}. Protocolo ${p.id_protocolo}:\n`;
            systemPrompt += `   - Assunto: ${p.assunto}\n`;
            systemPrompt += `   - Situação: ${p.situacao}\n`;
            systemPrompt += `   - Data: ${new Date(p.data_protocolo).toLocaleDateString('pt-BR')}\n\n`;
          });
        }
        
        if (dadosRelevantes.alvaras && dadosRelevantes.alvaras.length > 0) {
          systemPrompt += `**ALVARÁS E LICENÇAS RECENTES:**\n`;
          dadosRelevantes.alvaras.forEach((a, index) => {
            systemPrompt += `${index + 1}. Protocolo ${a.id_protocolo}:\n`;
            systemPrompt += `   - Tipo: ${a.assunto}\n`;
            systemPrompt += `   - Status: ${a.situacao}\n`;
            systemPrompt += `   - Departamento: ${a.departamento}\n\n`;
          });
        }
        
        systemPrompt += `**INSTRUÇÕES PARA USO DOS DADOS REAIS:**\n`;
        systemPrompt += `- SEMPRE use estes dados atualizados em suas respostas\n`;
        systemPrompt += `- Seja específico e preciso com números e datas\n`;
        systemPrompt += `- Cite protocolos específicos quando relevante\n`;
        systemPrompt += `- Nunca diga "não tenho acesso ao banco de dados" - você TEM acesso aos dados acima\n`;
        systemPrompt += `- Use os números reais para dar respostas concretas e úteis\n\n`;
        
        console.log('✅ [JS] Prompt construído com dados PostgreSQL!');
      }
    } catch (postgresError) {
      console.warn('⚠️ [JS] Erro ao buscar dados PostgreSQL:', postgresError.message);
    }
    
    // Construir mensagens para o DeepSeek
    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: message }
    ];
    
    // Fazer chamada para DeepSeek
    const startTime = Date.now();
    const completion = await deepseek.chat.completions.create({
      model: process.env.DEEPSEEK_MODEL || 'deepseek-chat',
      messages,
      temperature: parseFloat(process.env.DEEPSEEK_TEMPERATURE || '0.1'),
      max_tokens: parseInt(process.env.DEEPSEEK_MAX_TOKENS || '3000'),
      stream: false,
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // Extrair resposta
    const responseContent = completion.choices[0]?.message?.content || 'Desculpe, não consegui processar sua solicitação.';
    
    // Calcular tokens (estimativa)
    const tokens = {
      prompt: completion.usage?.prompt_tokens || estimateTokens(messages),
      completion: completion.usage?.completion_tokens || estimateTokens(responseContent),
      total: completion.usage?.total_tokens || 0
    };
    
    if (tokens.total === 0) {
      tokens.total = tokens.prompt + tokens.completion;
    }
    
    // Calcular custos básicos
    const cost = {
      prompt: tokens.prompt * 0.00007,
      completion: tokens.completion * 0.0011,
      total: 0,
      withDiscount: 0,
      savings: 0
    };
    cost.total = cost.prompt + cost.completion;
    cost.withDiscount = cost.total;
    
    console.log(`✅ [JS] Resposta gerada em ${responseTime}ms`);
    console.log(`📊 [JS] Tokens: ${tokens.total} | Custo: $${cost.total.toFixed(4)}`);
    
    return {
      content: responseContent,
      tokens,
      cost,
      model: completion.model,
      timestamp: new Date(),
      source: 'javascript-version'
    };
    
  } catch (error) {
    console.error('❌ [JS] Erro ao processar mensagem:', error);
    throw new Error(`Erro ao processar mensagem: ${error.message}`);
  }
}

// Função para obter prompt do sistema
function getSystemPrompt(secretaria) {
  return `Você é um assistente virtual especializado da Secretaria de ${secretaria} da Prefeitura de Valparaíso de Goiás.

Seja sempre cordial, profissional e use as informações reais do município que foram fornecidas no contexto.`;
}

// Função auxiliar para estimar tokens
function estimateTokens(text) {
  if (Array.isArray(text)) {
    const fullText = text.map(msg => 
      typeof msg === 'object' && 'content' in msg ? msg.content : ''
    ).join(' ');
    return estimateTokens(fullText);
  }
  return Math.ceil(text.length / 4);
}

module.exports = {
  processMessage
};