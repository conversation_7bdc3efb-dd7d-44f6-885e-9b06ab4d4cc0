import { Pool, PoolClient } from 'pg';

export interface ProtocoloInfo {
  id: number;
  id_protocolo: string;
  data_protocolo: string;
  assunto?: string;
  situacao?: string;
  departamento?: string;
}

export interface EstatisticasGerais {
  protocolos: number;
  requisicoes: number;
  servicos: number;
  departamentos: number;
}

export interface ServicoMunicipal {
  id: number;
  descricao: string;
  id_departamento?: number;
  departamento?: string;
  ativo: boolean;
}

export class PostgreSQLQueryService {
  private pool: Pool;

  constructor() {
    this.pool = new Pool({
      host: '*************',
      port: 5411,
      user: 'otto',
      password: 'otto',
      database: 'pv_valparaiso',
      max: 10,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    // Configurar eventos do pool
    this.pool.on('error', (err) => {
      console.error('Erro no pool PostgreSQL:', err);
    });
  }

  /**
   * Buscar protocolos por termo específico
   */
  async buscarProtocolos(termo?: string, limite = 10): Promise<ProtocoloInfo[]> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT DISTINCT
          p.id,
          p.id_protocolo,
          p.data_protocolo::text as data_protocolo,
          COALESCE(a.descricao, 'Não informado') as assunto,
          COALESCE(s.descricao, 'Não informado') as situacao,
          COALESCE(d.descricao, 'Não informado') as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE ($1 IS NULL OR
          LOWER(a.descricao) LIKE LOWER($1) OR
          LOWER(s.descricao) LIKE LOWER($1) OR
          p.id_protocolo::text LIKE $1)
        ORDER BY data_protocolo DESC
        LIMIT $2
      `;
      
      const result = await client.query(query, [
        termo ? `%${termo}%` : null, 
        limite
      ]);
      
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Obter estatísticas gerais do sistema
   */
  async obterEstatisticas(): Promise<EstatisticasGerais> {
    const client = await this.pool.connect();
    try {
      const queries = await Promise.all([
        client.query('SELECT COUNT(*) as total FROM protocolo_virtual_processos'),
        client.query('SELECT COUNT(*) as total FROM requisicao'),
        client.query('SELECT COUNT(*) as total FROM servicos'),
        client.query('SELECT COUNT(*) as total FROM departamentos')
      ]);

      return {
        protocolos: parseInt(queries[0].rows[0].total),
        requisicoes: parseInt(queries[1].rows[0].total),
        servicos: parseInt(queries[2].rows[0].total),
        departamentos: parseInt(queries[3].rows[0].total)
      };
    } finally {
      client.release();
    }
  }

  /**
   * Buscar protocolo específico por número
   */
  async buscarProtocoloPorNumero(numero: string): Promise<ProtocoloInfo | null> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT 
          p.id,
          p.id_protocolo,
          p.data_protocolo::text,
          COALESCE(a.descricao, 'Não informado') as assunto,
          COALESCE(s.descricao, 'Não informado') as situacao,
          COALESCE(d.descricao, 'Não informado') as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE p.id_protocolo = $1
      `;
      
      const result = await client.query(query, [numero]);
      return result.rows[0] || null;
    } finally {
      client.release();
    }
  }

  /**
   * Listar serviços municipais por departamento
   */
  async listarServicos(departamentoId?: number): Promise<ServicoMunicipal[]> {
    const client = await this.pool.connect();
    try {
      const query = departamentoId 
        ? `SELECT s.*, d.descricao as departamento 
           FROM servicos s 
           LEFT JOIN departamentos d ON s.id_departamento = d.id
           WHERE s.id_departamento = $1
           ORDER BY s.descricao`
        : `SELECT s.*, d.descricao as departamento 
           FROM servicos s 
           LEFT JOIN departamentos d ON s.id_departamento = d.id
           ORDER BY s.descricao`;
      
      const params = departamentoId ? [departamentoId] : [];
      const result = await client.query(query, params);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar protocolos por situação
   */
  async buscarProtocolosPorSituacao(situacao: string, limite = 20): Promise<ProtocoloInfo[]> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT 
          p.id,
          p.id_protocolo,
          p.data_protocolo::text,
          a.descricao as assunto,
          s.descricao as situacao,
          d.descricao as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE LOWER(s.descricao) LIKE LOWER($1)
        ORDER BY p.data_protocolo DESC
        LIMIT $2
      `;
      
      const result = await client.query(query, [`%${situacao}%`, limite]);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Obter estatísticas por departamento
   */
  async obterEstatisticasPorDepartamento() {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT 
          d.descricao as departamento,
          COUNT(p.id) as total_protocolos,
          COUNT(CASE WHEN s.descricao ILIKE '%andamento%' OR s.descricao ILIKE '%análise%' THEN 1 END) as em_andamento,
          COUNT(CASE WHEN s.descricao ILIKE '%concluído%' OR s.descricao ILIKE '%aprovado%' THEN 1 END) as concluidos
        FROM departamentos d
        LEFT JOIN protocolo_virtual_processos p ON p.departamento_atual = d.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        WHERE 1=1
        GROUP BY d.id, d.descricao
        HAVING COUNT(p.id) > 0
        ORDER BY total_protocolos DESC
        LIMIT 10
      `;
      
      const result = await client.query(query);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar alvarás especificamente
   */
  async buscarAlvaras(limite = 10): Promise<ProtocoloInfo[]> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT 
          p.id,
          p.id_protocolo,
          p.data_protocolo::text,
          a.descricao as assunto,
          s.descricao as situacao,
          d.descricao as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE LOWER(a.descricao) LIKE '%alvará%' 
           OR LOWER(a.descricao) LIKE '%licença%'
           OR LOWER(a.descricao) LIKE '%funcionamento%'
        ORDER BY p.data_protocolo DESC
        LIMIT $1
      `;
      
      const result = await client.query(query, [limite]);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Testar conexão com o banco
   */
  async testarConexao(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      return true;
    } catch (error) {
      console.error('Erro ao testar conexão PostgreSQL:', error);
      return false;
    }
  }

  /**
   * Fechar conexões do pool
   */
  async fecharConexoes(): Promise<void> {
    await this.pool.end();
  }

  /**
   * Buscar protocolos sem referência a requerente (corrigido)
   */
  async buscarProtocolosComRequerente(termo?: string, limite = 10): Promise<any[]> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT DISTINCT
          p.id,
          p.id_protocolo,
          p.data_protocolo::text as data_protocolo,
          COALESCE(a.descricao, 'Não informado') as assunto,
          COALESCE(s.descricao, 'Não informado') as situacao,
          COALESCE(d.descricao, 'Não informado') as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE ($1 IS NULL OR
          LOWER(a.descricao) LIKE LOWER($1) OR
          LOWER(s.descricao) LIKE LOWER($1) OR
          p.id_protocolo::text LIKE $1)
        ORDER BY data_protocolo DESC
        LIMIT $2
      `;
      
      const result = await client.query(query, [
        termo ? `%${termo}%` : null, 
        limite
      ]);
      
      return result.rows;
    } finally {
      client.release();
    }
  }
}