import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { clsx } from 'clsx';

interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'info' | 'warning' | 'error' | 'success';
  message: string;
  source: string;
  details?: any;
}

interface LogsViewerProps {
  className?: string;
}

export function LogsViewer({ className }: LogsViewerProps) {
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  
  // Mock logs - em produção viria da API
  const mockLogs: LogEntry[] = [
    {
      id: '1',
      timestamp: new Date(Date.now() - 2 * 60 * 1000),
      level: 'success',
      message: 'Mensagem processada com sucesso - Cache hit',
      source: 'ChatController',
      details: { userId: 'user123', processingTime: '45ms', cacheType: 'semantic' }
    },
    {
      id: '2', 
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      level: 'info',
      message: 'DeepSeek API chamada realizada',
      source: 'DeepSeekService',
      details: { cost: 0.00219, tokens: { input: 150, output: 300 } }
    },
    {
      id: '3',
      timestamp: new Date(Date.now() - 8 * 60 * 1000),
      level: 'warning',
      message: 'ChromaDB respondendo lentamente',
      source: 'RAGService', 
      details: { responseTime: '2.3s', threshold: '1s' }
    },
    {
      id: '4',
      timestamp: new Date(Date.now() - 12 * 60 * 1000),
      level: 'error',
      message: 'Falha na conexão com PostgreSQL - Tentativa de reconexão',
      source: 'PostgreSQLRepository',
      details: { error: 'Connection timeout', retryAttempt: 2 }
    },
    {
      id: '5',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      level: 'info',
      message: 'Cache limpo automaticamente - Limite de memória atingido',
      source: 'CacheService',
      details: { clearedEntries: 1250, memoryFreed: '45MB' }
    }
  ];

  const filteredLogs = selectedLevel === 'all' 
    ? mockLogs 
    : mockLogs.filter(log => log.level === selectedLevel);

  const getLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'success':
        return 'text-green-700 bg-green-100 border-green-200';
      case 'info':
        return 'text-blue-700 bg-blue-100 border-blue-200';
      case 'warning':
        return 'text-yellow-700 bg-yellow-100 border-yellow-200';
      case 'error':
        return 'text-red-700 bg-red-100 border-red-200';
      default:
        return 'text-gray-700 bg-gray-100 border-gray-200';
    }
  };

  const getLevelIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'success':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-pv-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>Logs do Sistema</span>
          </CardTitle>
          
          {/* Filter buttons */}
          <div className="flex space-x-1">
            {['all', 'error', 'warning', 'info', 'success'].map((level) => (
              <button
                key={level}
                onClick={() => setSelectedLevel(level)}
                className={clsx(
                  'px-3 py-1 text-xs font-medium rounded-full transition-colors',
                  selectedLevel === level
                    ? 'bg-pv-blue-100 text-pv-blue-800'
                    : 'text-pv-gray-600 hover:bg-pv-gray-100'
                )}
              >
                {level === 'all' ? 'Todos' : level.charAt(0).toUpperCase() + level.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredLogs.map((log) => (
            <div
              key={log.id}
              className={clsx(
                'p-3 rounded-lg border-l-4',
                getLevelColor(log.level)
              )}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getLevelIcon(log.level)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">{log.message}</p>
                    <span className="text-xs text-gray-500">
                      {log.timestamp.toLocaleTimeString('pt-BR')}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 mt-1">
                    <span className="text-xs text-gray-600">
                      Fonte: {log.source}
                    </span>
                    
                    {log.details && (
                      <details className="text-xs">
                        <summary className="cursor-pointer text-pv-blue-600 hover:text-pv-blue-800">
                          Ver detalhes
                        </summary>
                        <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-x-auto">
                          {JSON.stringify(log.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {filteredLogs.length === 0 && (
            <div className="text-center py-8 text-pv-gray-500">
              <svg className="w-12 h-12 mx-auto mb-2 text-pv-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p>Nenhum log encontrado para este filtro</p>
            </div>
          )}
        </div>
        
        <div className="mt-4 pt-4 border-t border-pv-gray-200 flex items-center justify-between text-sm text-pv-gray-600">
          <span>{filteredLogs.length} entradas exibidas</span>
          <button className="text-pv-blue-600 hover:text-pv-blue-800">
            Exportar logs
          </button>
        </div>
      </CardContent>
    </Card>
  );
}