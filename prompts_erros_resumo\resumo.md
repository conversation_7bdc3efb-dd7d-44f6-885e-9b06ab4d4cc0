🚀 Servidor rodando na porta 3001
📊 Health check: http://localhost:3001/health
🎯 Chat API: http://localhost:3001/api/chat/message
💰 Sistema de cache e desconto ativo!
⚡ Economia esperada: 60-70% com cache + horários de desconto
🚀 Processamento IMEDIATO para administracao
🤖 Processando mensagem para administracao: Temos acesso aos protocolos aberto no banco de dad...
🤖 [TS] Processando mensagem para administracao - VERSÃO TYPESCRIPT
🔍 [TS] Arquivo: deepSeekService.ts sendo executado
🔍 [DEBUG] Mensagem original: Temos acesso aos protocolos aberto no banco de dados?
🔍 [DEBUG] Mensagem lowercase: temos acesso aos protocolos aberto no banco de dados?
🔍 [DEBUG] Verificações individuais:
  - protocolo: true
  - quantos: false
  - dados: true
  - total: false
  - municipal: false
🔍 [DEBUG] isProtocolQuery FINAL: true
🔍 Buscando dados PostgreSQL para consulta de protocolos...
🔍 [DEBUG] Executando consultas PostgreSQL...
🔍 [DEBUG] 1. Buscando estatísticas...
✅ [DEBUG] Estatísticas obtidas: {
  protocolos: 26760,
  requisicoes: 339475,
  servicos: 145,
  departamentos: 500
}
🔍 [DEBUG] 2. Buscando protocolos recentes...
❌ [DEBUG] Erro ao buscar protocolos recentes: error: could not determine data type of parameter $1
    at D:\PROJETOS-BACKUP\prefeitura_virtual_ia\node_modules\pg\lib\client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async PostgreSQLQueryService.buscarProtocolosComRequerente (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\services\PostgreSQLQueryService.ts:301:22)
    at async processMessage (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\services\deepSeekService.ts:136:32)
    at async processMessage (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\chatController.ts:45:32) {
  length: 133,
  severity: 'ERROR',
  code: '42P08',
  detail: undefined,
  hint: undefined,
  position: '573',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_param.c',
  line: '307',
  routine: 'check_parameter_resolution_walker'
}
⚠️ Erro ao buscar dados PostgreSQL, continuando com prompt padrão: could not determine data type of parameter $1
🔍 Buscando documentos relevantes via RAG...
🔍 Busca híbrida: "Temos acesso aos protocolos aberto no banco de dados?" | Secretaria: administracao
🔄 Inicializando Simple RAG Service...
✅ Vector store carregado: 276 documentos
✅ Simple RAG Service inicializado com sucesso
🔍 Buscando: "Temos acesso aos protocolos aberto no banco de dados?" (top-6, threshold: 0.5)
✅ Encontrados 0 documentos relevantes
✅ Busca híbrida: 3 documentos finais
✅ RAG: 3 documentos encontrados em 1160ms
🔍 Debug tokens: {
  raw_usage: {
    prompt_tokens: 794,
    completion_tokens: 319,
    total_tokens: 1113,
    prompt_tokens_details: { cached_tokens: 768 },
    prompt_cache_hit_tokens: 768,
    prompt_cache_miss_tokens: 26
  },
  calculated: { prompt: 794, completion: 319, total: 1113 }
}
💰 Debug custos: {
  isDiscount: false,
  tokens: { prompt: 794, completion: 319, total: 1113 },
  costs: { promptCost: 0.00021438000000000002, completionCost: 0.0003509 },
  totalCost: 0.00056528,
  withDiscount: 0.00056528,
  savings: 0
}
✅ Resposta gerada em 22617ms
📊 Tokens: 1113 | Custo: $0.0006 | Com desconto: $0.0006
💰 Custo registrado: $0.0006 | Tokens: 1113 | Cache: MISS
📊 Rate limit atualizado - User: user, Messages: +1, Tokens: +500