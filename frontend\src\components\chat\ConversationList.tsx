import { useState } from 'react';
import { Button } from '@/components/ui';
import { clsx } from 'clsx';

interface ConversationItem {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
}

interface ConversationListProps {
  conversations?: ConversationItem[];
  activeConversationId?: string;
  onSelectConversation?: (conversationId: string) => void;
  onNewConversation?: () => void;
  onDeleteConversation?: (conversationId: string) => void;
}

export function ConversationList({ 
  conversations = [], 
  activeConversationId,
  onSelectConversation,
  onNewConversation,
  onDeleteConversation
}: ConversationListProps) {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredConversations = conversations.filter(conv =>
    conv.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      return timestamp.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
    } else if (days === 1) {
      return 'Ontem';
    } else if (days < 7) {
      return `${days} dias`;
    } else {
      return timestamp.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
    }
  };

  return (
    <div className="w-80 bg-pv-gray-50 border-r border-pv-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-pv-gray-200">
        <Button 
          onClick={onNewConversation}
          className="w-full mb-4"
          variant="primary"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Nova Conversa
        </Button>

        {/* Search */}
        <div className="relative">
          <svg className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-pv-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <input
            type="text"
            placeholder="Buscar conversas..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-pv-gray-300 rounded-lg focus:border-pv-blue-primary focus:ring-2 focus:ring-pv-blue-500 focus:ring-offset-2 text-sm"
          />
        </div>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto">
        {filteredConversations.length === 0 ? (
          <div className="p-4 text-center text-pv-gray-500">
            <svg className="w-12 h-12 mx-auto mb-2 text-pv-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <p className="text-sm">
              {searchTerm ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa ainda'}
            </p>
            <p className="text-xs mt-1">
              {!searchTerm && 'Inicie uma nova conversa para começar'}
            </p>
          </div>
        ) : (
          <div className="space-y-1 p-2">
            {filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                className={clsx(
                  'group relative p-3 rounded-lg cursor-pointer transition-colors',
                  activeConversationId === conversation.id
                    ? 'bg-pv-blue-100 border border-pv-blue-200'
                    : 'hover:bg-white border border-transparent'
                )}
                onClick={() => onSelectConversation?.(conversation.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className={clsx(
                      'text-sm font-medium truncate',
                      activeConversationId === conversation.id
                        ? 'text-pv-blue-800'
                        : 'text-pv-gray-800'
                    )}>
                      {conversation.title}
                    </h4>
                    <p className={clsx(
                      'text-xs truncate mt-1',
                      activeConversationId === conversation.id
                        ? 'text-pv-blue-600'
                        : 'text-pv-gray-600'
                    )}>
                      {conversation.lastMessage}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className={clsx(
                        'text-xs',
                        activeConversationId === conversation.id
                          ? 'text-pv-blue-500'
                          : 'text-pv-gray-500'
                      )}>
                        {formatTimestamp(conversation.timestamp)}
                      </span>
                      <span className={clsx(
                        'text-xs px-2 py-0.5 rounded-full',
                        activeConversationId === conversation.id
                          ? 'bg-pv-blue-200 text-pv-blue-800'
                          : 'bg-pv-gray-200 text-pv-gray-600'
                      )}>
                        {conversation.messageCount} msgs
                      </span>
                    </div>
                  </div>

                  {/* Delete button */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteConversation?.(conversation.id);
                    }}
                    className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-red-100 rounded text-red-500"
                    title="Deletar conversa"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-pv-gray-200 bg-white">
        <div className="text-xs text-pv-gray-500 text-center">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Sistema Online</span>
          </div>
          <div className="mt-1">
            {conversations.length} conversas salvas
          </div>
        </div>
      </div>
    </div>
  );
}